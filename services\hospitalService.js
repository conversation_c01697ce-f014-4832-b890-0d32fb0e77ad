/**
 * 医院相关接口服务 - 更新版本，符合API文档规范
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 查询宠物医院（普通用户）--已检查，严格按照接口文档
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始 (必需)
 * @param {number} params.pageSize - 每页数量 (必需)
 * @param {string} params.address - 用户当前所处的位置 (必需)
 * @returns {Promise} 医院列表
 * 接口: POST /users/common/search/hospital
 * 参数: page, pageSize, address (Body), Authorization (Header)
 * 返回: { code, message, total, data: [{ID, address, name, contact, license, photo}] }
 */
function getHospitalList(params = {}) {
  console.log('🚀 查询宠物医院请求:', params);

  return new Promise((resolve, reject) => {
    try {
      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 参数验证 - 严格按照接口文档要求
      const errors = validateHospitalSearchParams(params);
      if (errors.length > 0) {
        reject(new Error(errors[0]));
        return;
      }

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 医院列表使用本地Mock数据');
        const mockData = getMockHospitalDataForAPI(params);
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '查询成功',
          total: mockData.total,
          data: mockData.list
        });
        return;
      }

      // 构造请求体数据
      const requestData = {
        page: parseInt(params.page),
        pageSize: parseInt(params.pageSize),
        address: params.address
      };

      console.log('🏥 发送医院查询请求（POST）:', requestData);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.post(CONFIG.API_PATHS.HOSPITAL_SEARCH, requestData, requestOptions).then(result => {
        console.log('✅ 医院列表响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 查询医院列表成功');

          // 处理返回数据，转换为前端需要的格式
          let processedData = result.data || [];
          if (Array.isArray(processedData)) {
            processedData = processedData.map(item => ({
              ID: item.ID,
              address: item.address,
              name: item.name,
              contact: item.contact,
              license: item.license,
              photo: item.photo
            }));
          }

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '查询成功',
            total: result.total || processedData.length,
            data: processedData
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '查询医院列表失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 医院列表接口调用失败:', error);
        // 返回模拟数据作为降级方案
        const mockData = getMockHospitalDataForAPI(params);
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '接口失败，使用本地数据',
          total: mockData.total,
          data: mockData.list
        });
      });

    } catch (err) {
      console.error('❌ 查询医院列表参数准备失败:', err);
      reject(new Error('查询医院列表失败，请检查参数'));
    }
  });
}

/**
 * 处理医院数据
 * @param {Array} apiData - API返回的原始数据
 * @returns {Array} 处理后的数据
 */
function processHospitalData(apiData) {
  if (!Array.isArray(apiData)) {
    console.warn('⚠️ API返回的data不是数组格式');
    return [];
  }

  return apiData.map(item => {
    // 处理图片路径
    let imageUrl = item.photo || '/assets/images/default-pet.png';

    // 根据API文档要求处理图片路径
    if (imageUrl && !imageUrl.startsWith('http')) {
      // 相对路径需要拼接D:/images/
      imageUrl = `D:/images${imageUrl}`;
    }

    // 转换数据格式以匹配前端组件需求
    return {
      id: item.ID || item.id || Date.now() + Math.random(), // API返回ID字段
      name: item.name || '未命名医院',
      type: extractServiceType(item.contact, item.license), // 从联系方式或执照信息推断类型
      rating: generateRating(), // API没有评分字段，生成随机评分
      image: imageUrl,
      address: item.address || '未知地址',
      description: generateDescription(item.name, item.license), // 根据医院信息生成描述
      businessHours: extractBusinessHours(item.contact), // 从联系方式提取营业时间
      contact: item.contact || '暂无联系方式',
      license: item.license || '暂无执照信息',

      // 额外字段用于详情页面
      distance: generateDistance(),
      services: generateServices(),
      features: generateFeatures(),
      doctorCount: generateDoctorCount(),
      establishedYear: generateEstablishedYear()
    };
  });
}

/**
 * 从联系方式或执照信息推断服务类型
 */
function extractServiceType(contact, license) {
  const types = ['常规诊疗', '疫苗接种', '手术治疗', '美容服务', '寄养服务'];
  const keywords = {
    '常规诊疗': ['诊疗', '体检', '看病', '治疗'],
    '疫苗接种': ['疫苗', '免疫', '预防'],
    '手术治疗': ['手术', '外科', '治疗'],
    '美容服务': ['美容', '洗护', '造型'],
    '寄养服务': ['寄养', '托管', '照看']
  };

  const text = (contact + ' ' + license).toLowerCase();

  for (const [type, words] of Object.entries(keywords)) {
    if (words.some(word => text.includes(word))) {
      return type;
    }
  }

  return types[Math.floor(Math.random() * types.length)];
}

/**
 * 根据医院信息生成描述
 */
function generateDescription(name, license) {
  const templates = [
    `${name}是一家专业的宠物医疗机构，致力于为您的爱宠提供优质的医疗服务。`,
    `专业宠物医院，拥有先进的医疗设备和经验丰富的兽医团队。`,
    `为宠物提供全方位的医疗保健服务，让您的爱宠健康快乐。`,
    `资深兽医团队，专业设备齐全，是您身边可信赖的宠物健康专家。`
  ];

  return templates[Math.floor(Math.random() * templates.length)];
}

/**
 * 从联系方式提取营业时间（简单实现）
 */
function extractBusinessHours(contact) {
  // 尝试从联系方式中提取时间信息
  const timePattern = /(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/;
  const match = contact?.match(timePattern);

  if (match) {
    return `${match[1]}:${match[2]}-${match[3]}:${match[4]}`;
  }

  // 默认营业时间
  const defaultHours = [
    '08:00-20:00',
    '09:00-21:00',
    '08:30-19:30',
    '07:00-22:00',
    '09:00-18:00'
  ];

  return defaultHours[Math.floor(Math.random() * defaultHours.length)];
}

/**
 * 生成随机评分
 */
function generateRating() {
  return Number((4.0 + Math.random() * 1.0).toFixed(1));
}

/**
 * 生成随机距离
 */
function generateDistance() {
  return `${(Math.random() * 5).toFixed(1)}km`;
}

/**
 * 生成服务项目
 */
function generateServices() {
  const allServices = ['体检', '疫苗', '手术', '急诊', '美容', '寄养', '训练', '康复'];
  const count = 3 + Math.floor(Math.random() * 3);
  return allServices.sort(() => 0.5 - Math.random()).slice(0, count);
}

/**
 * 生成特色功能
 */
function generateFeatures() {
  const allFeatures = ['24小时急诊', '停车方便', '设备先进', '医生专业', '环境优雅', '价格合理', '服务周到'];
  const count = 3 + Math.floor(Math.random() * 2);
  return allFeatures.sort(() => 0.5 - Math.random()).slice(0, count);
}

/**
 * 生成医生数量
 */
function generateDoctorCount() {
  return 4 + Math.floor(Math.random() * 12);
}

/**
 * 生成成立年份
 */
function generateEstablishedYear() {
  return 2010 + Math.floor(Math.random() * 14);
}

/**
 * 评价宠物医院（普通用户）--已检查，严格按照接口文档
 * @param {Object} data - 评价数据
 * @param {number} data.hospitalID - 医院ID（必填）
 * @param {number} data.rating - 评分（1-5）
 * @param {string} data.content - 评价内容（≤500字）
 * @returns {Promise} 评价结果
 * 接口: POST /users/common/evaluate/hospital
 * 参数: hospitalID (integer), rating (integer), content (string), Authorization (Header)
 * 返回: {code: 200, message: "成功", data: {}}
 */
function evaluateHospital(data) {
  // ✅ 参数校验
  if (!data.hospitalID || !Number.isInteger(data.hospitalID)) {
    return Promise.reject(new Error('医院ID不能为空且必须为整数'));
  }

  if (!data.rating || data.rating < 1 || data.rating > 5) {
    return Promise.reject(new Error('评分必须在1到5之间'));
  }

  if (!data.content || !data.content.trim()) {
    return Promise.reject(new Error('评价内容不能为空'));
  }

  if (data.content.length > 500) {
    return Promise.reject(new Error('评价内容不能超过500字'));
  }

  // ✅ 模拟数据逻辑
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 医院评价使用本地Mock数据');
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: { id: Date.now() },
      message: '评价成功（Mock）'
    });
  }

  // ✅ 获取 token 并设置请求头
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject(new Error('请先登录'));
  }

  return request.post(CONFIG.API_PATHS.EVALUATE_HOSPITAL, {
    hospitalID: data.hospitalID,
    rating: data.rating,
    content: data.content.trim()
  }, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      Authorization: token
    }
  }).then(res => {
    if (res && res.code === 200) {
      return res;
    } else {
      throw new Error(res.message || '评价失败');
    }
  }).catch(err => {
    console.error('🔥 医院评价接口调用失败:', err);
    throw err;
  });
}

/**
 * 查看用户对宠物医院的评价（普通用户）--已检查，严格按照接口文档
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始 (必需)
 * @param {number} params.pageSize - 每页数量 (必需)
 * @param {number} params.hospitalID - 医院ID (必需)
 * @returns {Promise} 评价列表
 * 接口: POST /users/common/evaluation/hospital
 * 参数: page (integer), pageSize (integer), hospitalID (integer), Authorization (Header)
 * 返回: {code, message, total, data: [{username, content, avatar, rating}]}
 */
function getHospitalEvaluations(params = {}) {
  const { hospitalID, page, pageSize } = params;

  // ✅ 参数校验
  if (!hospitalID || !Number.isInteger(hospitalID)) {
    return Promise.reject(new Error('hospitalID 是必需的并且必须为整数'));
  }
  if (!page || !Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('page 是必需的并且必须为大于0的整数'));
  }
  if (!pageSize || !Number.isInteger(pageSize) || pageSize < 1) {
    return Promise.reject(new Error('pageSize 是必需的并且必须为大于0的整数'));
  }

  // ✅ 处理本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 医院评价使用本地Mock数据');
    const mockData = getMockHospitalEvaluationData();
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      total: mockData.length,
      data: mockData,
      message: '使用本地Mock数据'
    });
  }

  // ✅ 构建请求体
  const body = { hospitalID, page, pageSize };

  // ✅ 认证头处理
  const token = wx.getStorageSync('token');
  const headers = {};
  if (token) {
    headers.Authorization = token;
  }

  return request.post(CONFIG.API_PATHS.HOSPITAL_EVALUATION, body, {
    headers,
    useMock: CONFIG.USE_MOCK,
    hideError: true
  }).then(res => {
    if (res.code !== 200) throw new Error(res.message || '接口返回错误');
    return {
      code: res.code,
      message: res.message,
      total: res.total || 0,
      data: res.data || []
    };
  }).catch(error => {
    console.error('🔥 医院评价接口调用失败:', error);
    const mockData = getMockHospitalEvaluationData();
    return {
      code: CONFIG.ERROR_CODES.SUCCESS,
      message: '接口失败，使用本地数据',
      total: mockData.length,
      data: mockData
    };
  });
}


/**
 * 查看自己对医院的评价（普通用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getSelfHospitalEvaluations(params = {}) {
  // 🎯 直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 自己医院评价使用本地Mock数据');
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: getMockSelfEvaluationData(),
      message: '使用本地Mock数据'
    });
  }

  return request.get(CONFIG.API_PATHS.GET_SELF_HOSPITAL_EVALUATIONS, params, {
    useMock: CONFIG.USE_MOCK,
    hideError: true
  }).catch(error => {
    console.error('🔥 自己医院评价接口调用失败:', error);
    return {
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: getMockSelfEvaluationData(),
      message: '接口失败，使用本地数据'
    };
  });
}

/**
 * 删除对宠物医院的评价（普通用户）
 * @param {string} evaluationId - 评价ID
 * @returns {Promise} 删除结果
 */
function deleteHospitalEvaluation(evaluationId) {
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 删除医院评价使用本地Mock数据');
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: {},
      message: '删除成功'
    });
  }

  return request.delete(`${CONFIG.API_PATHS.DELETE_HOSPITAL_EVALUATION}/${evaluationId}`, {}, {
    useMock: CONFIG.USE_MOCK
  });
}


/**
 * 预约宠物医疗（普通用户）--已检查，严格按照接口文档
 * @param {Object} data - 预约数据
 * @param {number} data.hospitalID - 医院ID (必需)
 * @param {string} data.appointmentStatus - 预约状态 (必需)
 * @param {string} data.appointmentTime - 预约时间，格式：YYYY-MM-DD HH:mm:ss (必需)
 * @param {string} data.animalHealthyStatus - 动物健康状况 (必需)
 * @param {string} data.contact - 用户联系方式 (必需)
 * @returns {Promise} 预约结果
 * 接口: POST /users/common/hospital/reserve
 * 参数: hospitalID (integer), appointmentStatus (string), appointmentTime (string), animalHealthyStatus (string), contact (string), Authorization (Header)
 * 返回: {code: 200, message: "成功", data: {}}
 */
function reserveMedical(data) {
  console.log('🚀 预约宠物医疗请求:', data);

  return new Promise((resolve, reject) => {
    try {
      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 参数验证 - 严格按照接口文档要求
      const errors = validateHospitalReserveParams(data);
      if (errors.length > 0) {
        reject(new Error(errors[0]));
        return;
      }

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 医疗预约使用本地Mock数据');
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '成功',
          data: {}
        });
        return;
      }

      // 构造请求体数据 - 严格按照接口文档格式
      const requestData = {
        hospitalID: parseInt(data.hospitalID),
        appointmentStatus: data.appointmentStatus,
        appointmentTime: data.appointmentTime,
        animalHealthyStatus: data.animalHealthyStatus,
        contact: data.contact
      };

      console.log('🏥 发送医疗预约请求（POST）:', requestData);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.post(CONFIG.API_PATHS.HOSPITAL_RESERVE, requestData, requestOptions).then(result => {
        console.log('✅ 医疗预约响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 医疗预约成功');

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '成功',
            data: result.data || {}
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '预约失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 医疗预约接口调用失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 预约医疗参数准备失败:', err);
      reject(new Error('预约失败，请检查参数'));
    }
  });
}

/**
 * 查看宠物医疗预约状态（普通用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 预约状态列表
 */
function getMedicalReserveStatus(params = {}) {
  // 🎯 直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 医疗预约状态使用本地Mock数据');
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: getMockReserveStatusData(),
      message: '使用本地Mock数据'
    });
  }

  return request.get(CONFIG.API_PATHS.MEDICAL_RESERVE_STATUS, params, {
    useMock: CONFIG.USE_MOCK,
    hideError: true
  }).catch(error => {
    console.error('🔥 医疗预约状态接口调用失败:', error);
    return {
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: getMockReserveStatusData(),
      message: '接口失败，使用本地数据'
    };
  });
}

/**
 * 删除宠物医疗预约状态（普通用户）
 * @param {string|number} appointmentId - 预约ID
 * @returns {Promise} 删除结果
 */
function deleteHospitalReserve(appointmentId) {
  // 参数验证 - 确保ID为正整数（根据API文档要求）
  if (!appointmentId || !Number.isInteger(Number(appointmentId)) || Number(appointmentId) <= 0) {
    console.error('❌ 预约ID参数无效:', appointmentId);
    return Promise.reject(new Error('预约ID必须是大于0的整数'));
  }

  // 获取认证令牌（根据API文档要求）
  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('❌ 用户未登录');
    return Promise.reject(new Error('请先登录'));
  }

  console.log('🗑️ 删除医疗预约，ID:', appointmentId);

  // 构造请求选项 - 添加Authorization头部（根据API文档要求）
  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  };

  // 发送DELETE请求到正确的端点
  return request.delete(`${CONFIG.API_PATHS.DELETE_HOSPITAL_RESERVE}/${appointmentId}`, {}, options)
    .then(response => {
      console.log('📥 删除预约响应:', response);

      // 检查响应格式 - 按照接口文档处理（200成功，500失败）
      if (response && response.code === 200) {
        console.log('✅ 预约删除成功');
        return response;
      } else if (response && response.code === 500) {
        console.error('❌ 服务器错误:', response.message);
        throw new Error(response.message || '服务器错误');
      } else {
        console.error('❌ 删除失败:', response);
        throw new Error(response.message || '删除预约失败');
      }
    })
    .catch(error => {
      console.error('🔥 删除预约失败:', error);

      // 处理特殊错误状态码（根据API文档要求）
      if (error.statusCode === 403 || error.code === 403) {
        error.message = error.message || '无权限删除此预约';
      } else if (error.statusCode === 404 || error.code === 404) {
        error.message = error.message || '预约不存在';
      }

      throw error;
    });
}

/**
 * 查看医疗预约情况（医院用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise<Object>} 预约列表
 */
function getReservesForHospital(params = {}) {
  console.log('🏥 获取医院预约情况，参数:', params);

  const { page = 1, pageSize = 10 } = params;

  // 参数验证
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }

  const requestData = { page, pageSize };

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  const token = wx.getStorageSync('token');
  if (token) {
    options.headers['Authorization'] = token;
  }

  return request.post(CONFIG.API_PATHS.HOSPITAL_RESERVES, requestData, options)
    .then(response => {
      console.log('📥 医疗预约接口响应:', response);

      if (response && response.code === 200) {
        const result = {
          code: response.code,
          message: response.message || '获取成功',
          total: response.total || 0,
          data: Array.isArray(response.data)
            ? response.data.map(item => ({
                id: item.ID,
                avatar: item.avatar || '',
                username: item.username || '',
                animalHealthStatus: item.animalHealthStatus || '',
                appointmentTime: item.appointmentTime || '',
                contact: item.contact || '',
                appointmentStatus: item.appointmentStatus || ''
              }))
            : []
        };

        return result;
      } else {
        throw new Error(response.message || '获取预约列表失败');
      }
    })
    .catch(error => {
      console.error('❌ 获取预约数据失败:', error);

      if (error.message.includes('网络')) {
        return {
          code: 200,
          message: '网络连接失败，请检查网络后重试',
          total: 0,
          data: []
        };
      }

      throw error;
    });
}


/**
 * 修改预约状态（医院用户）
 * @param {string|number} reserveId - 预约ID
 * @param {string} status - 要更新的预约状态
 * @returns {Promise<Object>} 更新结果
 */
function updateReserveStatus(reserveId, status) {
  if (!reserveId) {
    return Promise.reject(new Error('预约ID不能为空'));
  }
  if (!status) {
    return Promise.reject(new Error('预约状态不能为空'));
  }

  const url = `${CONFIG.API_PATHS.UPDATE_RESERVE_STATUS_BASE}/${reserveId}${status}`;

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  const token = wx.getStorageSync('token');
  if (token) {
    options.headers['Authorization'] = token;
  }

  return request.put(url, {}, options)  // 注意：请求体为空对象
    .then(response => {
      if (response.code === 200) {
        return {
          code: response.code,
          message: response.message || '更新成功',
          data: response.data || {}
        };
      } else {
        throw new Error(response.message || '修改预约状态失败');
      }
    })
    .catch(error => {
      console.error('❌ 修改预约状态失败:', error);
      throw error;
    });
}


/**
 * 查看评价（医院）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getEvaluationsForHospital(params = {}) {
  return request.get(CONFIG.API_PATHS.GET_HOSPITAL_EVALUATIONS, params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 获取模拟医院数据 - 符合API返回格式
 * @returns {Array} 模拟医院数据列表
 */
function getMockHospitalData() {
  return [
    {
      ID: 1,
      name: '爱宠动物医院',
      address: '北京市朝阳区三里屯街道宠物大厦3层',
      contact: '010-12345678 营业时间:08:00-22:00',
      license: '京兽医字第001号',
      photo: 'https://loremflickr.com/400/400?lock=2864249289477031'
    },
    {
      ID: 2,
      name: '宠康动物医院',
      address: '上海市浦东新区陆家嘴金融区宠物医疗中心',
      contact: '021-87654321 营业时间:09:00-21:00',
      license: '沪兽医字第002号',
      photo: 'https://loremflickr.com/400/400?lock=2864249289477032'
    },
    {
      ID: 3,
      name: '萌宠医疗中心',
      address: '广州市天河区珠江新城宠物健康大厦',
      contact: '020-98765432 营业时间:08:30-20:30',
      license: '粤兽医字第003号',
      photo: 'https://loremflickr.com/400/400?lock=2864249289477033'
    },
    {
      ID: 4,
      name: '心宠动物医院',
      address: '深圳市南山区科技园动物医疗大楼',
      contact: '0755-56789012 营业时间:07:00-23:00',
      license: '深兽医字第004号',
      photo: 'https://loremflickr.com/400/400?lock=2864249289477034'
    },
    {
      ID: 5,
      name: '宠物健康诊所',
      address: '成都市武侯区宠物医疗街88号',
      contact: '028-87654321 营业时间:09:00-18:00',
      license: '川兽医字第005号',
      photo: 'https://loremflickr.com/400/400?lock=2864249289477035'
    },
    {
      ID: 6,
      name: '蓝天动物医院',
      address: '杭州市西湖区宠物医疗园区',
      contact: '0571-12345678 营业时间:08:00-20:00',
      license: '浙兽医字第006号',
      photo: 'https://loremflickr.com/400/400?lock=2864249289477036'
    }
  ];
}

/**
 * 获取模拟医院评价数据--已检查，严格按照接口文档格式
 * @returns {Array} 模拟评价数据列表
 * 返回格式: [{username, content, avatar, rating}]
 */
function getMockHospitalEvaluationData() {
  return [
    {
      username: '宠物主人A',
      content: '医生很专业，设备先进，我家狗狗在这里治疗效果很好！强烈推荐。',
      avatar: 'https://avatars.githubusercontent.com/u/27064666',
      rating: 95
    },
    {
      username: '爱猫人士',
      content: '服务态度很好，医生耐心负责，就是价格稍微有点贵。',
      avatar: 'https://avatars.githubusercontent.com/u/27064667',
      rating: 82
    },
    {
      username: '小狗的爸爸',
      content: '24小时急诊真的很方便，半夜宠物生病也不用担心了。',
      avatar: 'https://avatars.githubusercontent.com/u/27064668',
      rating: 88
    },
    {
      username: '新手铲屎官',
      content: '第一次带宠物看病，医生很耐心地解释，学到了很多护理知识。',
      avatar: 'https://avatars.githubusercontent.com/u/27064669',
      rating: 76
    }
  ];
}

/**
 * 获取模拟自己评价数据
 * @returns {Array} 模拟自己评价数据列表
 */
function getMockSelfEvaluationData() {
  return [
    {
      id: 1,
      hospitalId: 1,
      hospitalName: '爱宠动物医院',
      rating: 5,
      content: '服务很好，医生专业，下次还会来的。',
      createTime: '2024-03-10 15:30',
      status: 'published'
    },
    {
      id: 2,
      hospitalId: 3,
      hospitalName: '萌宠医疗中心',
      rating: 4,
      content: '环境不错，价格合理，推荐给朋友们。',
      createTime: '2024-03-05 09:15',
      status: 'published'
    }
  ];
}

/**
 * 获取模拟预约状态数据
 * @returns {Array} 模拟预约状态数据列表
 */
function getMockReserveStatusData() {
  return [
    {
      id: 1,
      reserveNumber: 'MED20240315001',
      hospitalId: 1,
      hospitalName: '爱宠动物医院',
      petName: '小白',
      service: '体检',
      reserveTime: '2024-03-20 10:00',
      status: 'confirmed',
      statusText: '已确认',
      contact: '010-12345678',
      address: '北京市朝阳区三里屯街道宠物大厦3层',
      createTime: '2024-03-15 14:30'
    },
    {
      id: 2,
      reserveNumber: 'MED20240310002',
      hospitalId: 2,
      hospitalName: '宠康动物医院',
      petName: '咪咪',
      service: '疫苗接种',
      reserveTime: '2024-03-18 14:30',
      status: 'pending',
      statusText: '待确认',
      contact: '021-87654321',
      address: '上海市浦东新区陆家嘴金融区宠物医疗中心',
      createTime: '2024-03-10 11:20'
    },
    {
      id: 3,
      reserveNumber: 'MED20240305003',
      hospitalId: 4,
      hospitalName: '心宠动物医院',
      petName: '旺财',
      service: '手术治疗',
      reserveTime: '2024-03-15 09:00',
      status: 'completed',
      statusText: '已完成',
      contact: '0755-56789012',
      address: '深圳市南山区科技园动物医疗大楼',
      createTime: '2024-03-05 16:45'
    }
  ];
}

/**
 * 验证医院查询参数
 * @param {Object} params - 查询参数
 * @returns {Array} 错误信息数组
 */
function validateHospitalSearchParams(params) {
  const errors = [];

  // 必填字段检查
  if (!params.page || typeof params.page !== 'number') {
    errors.push('页码不能为空且必须为数字');
  }

  if (!params.pageSize || typeof params.pageSize !== 'number') {
    errors.push('每页数量不能为空且必须为数字');
  }

  if (!params.address || params.address.trim() === '') {
    errors.push('地址不能为空');
  }

  // 如果必填字段有问题，直接返回
  if (errors.length > 0) {
    return errors;
  }

  // 范围验证
  if (params.page < 1) {
    errors.push('页码必须大于等于1');
  }

  if (params.pageSize < 1 || params.pageSize > 100) {
    errors.push('每页数量必须在1-100之间');
  }

  return errors;
}

/**
 * 验证医院预约参数--已检查，严格按照接口文档
 * @param {Object} data - 预约数据
 * @returns {Array} 错误信息数组
 */
function validateHospitalReserveParams(data) {
  const errors = [];

  // 检查数据对象
  if (!data || typeof data !== 'object') {
    errors.push('预约数据不能为空');
    return errors;
  }

  // 必填字段检查 - hospitalID
  if (!data.hospitalID) {
    errors.push('医院ID不能为空');
  } else if (typeof data.hospitalID !== 'number' || !Number.isInteger(data.hospitalID)) {
    errors.push('医院ID必须为整数');
  } else if (data.hospitalID <= 0) {
    errors.push('医院ID必须大于0');
  }

  // 必填字段检查 - appointmentStatus
  if (!data.appointmentStatus) {
    errors.push('预约状态不能为空');
  } else if (typeof data.appointmentStatus !== 'string') {
    errors.push('预约状态必须为字符串');
  } else if (data.appointmentStatus.trim() === '') {
    errors.push('预约状态不能为空白字符');
  }

  // 必填字段检查 - appointmentTime
  if (!data.appointmentTime) {
    errors.push('预约时间不能为空');
  } else if (typeof data.appointmentTime !== 'string') {
    errors.push('预约时间必须为字符串');
  } else if (data.appointmentTime.trim() === '') {
    errors.push('预约时间不能为空白字符');
  } else {
    // 验证时间格式 YYYY-MM-DD HH:mm:ss
    const timePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
    if (!timePattern.test(data.appointmentTime)) {
      errors.push('预约时间格式必须为 YYYY-MM-DD HH:mm:ss');
    }
  }

  // 必填字段检查 - animalHealthyStatus
  if (!data.animalHealthyStatus) {
    errors.push('动物健康状况不能为空');
  } else if (typeof data.animalHealthyStatus !== 'string') {
    errors.push('动物健康状况必须为字符串');
  } else if (data.animalHealthyStatus.trim() === '') {
    errors.push('动物健康状况不能为空白字符');
  }

  // 必填字段检查 - contact
  if (!data.contact) {
    errors.push('联系方式不能为空');
  } else if (typeof data.contact !== 'string') {
    errors.push('联系方式必须为字符串');
  } else if (data.contact.trim() === '') {
    errors.push('联系方式不能为空白字符');
  }

  return errors;
}

/**
 * 获取符合接口文档的模拟医院数据 - 分页版本
 * @param {Object} params - 查询参数
 * @returns {Object} 分页的模拟医院数据
 */
function getMockHospitalDataForAPI(params = {}) {
  const { page = 1, pageSize = 10, address = '' } = params;

  // 完整的Mock数据列表，严格按照接口文档格式
  const allHospitals = [
    {
      ID: 1,
      address: '武汉市洪山区光谷大道88号',
      name: '光谷宠物医院',
      contact: '027-87654321',
      license: '鄂武卫动字第001号',
      photo: '/assets/images/hospital1.jpg'
    },
    {
      ID: 2,
      address: '武汉市江汉区解放大道168号',
      name: '江汉动物医院',
      contact: '027-85432109',
      license: '鄂武卫动字第002号',
      photo: '/assets/images/hospital2.jpg'
    },
    {
      ID: 3,
      address: '武汉市武昌区中南路99号',
      name: '中南宠物医疗中心',
      contact: '027-86543210',
      license: '鄂武卫动字第003号',
      photo: '/assets/images/hospital3.jpg'
    },
    {
      ID: 4,
      address: '武汉市硚口区建设大道288号',
      name: '硚口动物诊所',
      contact: '027-84321098',
      license: '鄂武卫动字第004号',
      photo: '/assets/images/hospital4.jpg'
    },
    {
      ID: 5,
      address: '武汉市青山区和平大道188号',
      name: '青山宠物医院',
      contact: '027-83210987',
      license: '鄂武卫动字第005号',
      photo: '/assets/images/hospital5.jpg'
    },
    {
      ID: 6,
      address: '北京市朝阳区三里屯街道12号',
      name: '朝阳宠物医院',
      contact: '010-12345678',
      license: '京朝卫动字第001号',
      photo: '/assets/images/hospital6.jpg'
    },
    {
      ID: 7,
      address: '上海市浦东新区陆家嘴金融区88号',
      name: '浦东动物医疗中心',
      contact: '021-87654321',
      license: '沪浦卫动字第001号',
      photo: '/assets/images/hospital7.jpg'
    },
    {
      ID: 8,
      address: '广州市天河区珠江新城A座',
      name: '天河宠物医院',
      contact: '020-98765432',
      license: '粤穗卫动字第001号',
      photo: '/assets/images/hospital8.jpg'
    }
  ];

  // 根据地址过滤
  let filteredHospitals = allHospitals;
  if (address && address.trim() !== '' && address !== '全国') {
    const keyword = address.toLowerCase().trim();
    filteredHospitals = allHospitals.filter(hospital =>
      hospital.name.toLowerCase().includes(keyword) ||
      hospital.address.toLowerCase().includes(keyword)
    );
  }

  // 分页处理
  const total = filteredHospitals.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const list = filteredHospitals.slice(startIndex, endIndex);

  return {
    total,
    list,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}

export default {
  getHospitalList,
  evaluateHospital,
  getHospitalEvaluations,
  getSelfHospitalEvaluations,
  deleteHospitalEvaluation,
  reserveMedical,
  getMedicalReserveStatus,
  deleteHospitalReserve,
  getReservesForHospital,
  updateReserveStatus,
  getEvaluationsForHospital
};